/**
 * 字幕网络拦截器
 * 
 * 核心功能:
 * - 拦截视频平台的字幕网络请求
 * - 支持 Fetch API 和 XMLHttpRequest 拦截
 * - 平台特定的 URL 模式匹配
 * - 智能过滤以优化性能
 */

import {
  SupportedPlatform,
  SubtitleData,
  SubtitleFormat,
  InterceptRule,
  InterceptorConfig,
  ISubtitleNetworkInterceptor,
  InterceptionStatus,
  SubtitleError,
  SubtitleErrorType
} from '../types';

export class SubtitleNetworkInterceptor implements ISubtitleNetworkInterceptor {
  private isActive = false;
  private interceptRules = new Map<SupportedPlatform, InterceptRule[]>();
  private captureCallback?: (data: SubtitleData) => void;
  private config: InterceptorConfig;
  private compiledPatterns = new Map<string, RegExp>();
  private originalFetch: typeof window.fetch;
  private originalXHROpen: typeof XMLHttpRequest.prototype.open;
  private originalXHRSend: typeof XMLHttpRequest.prototype.send;

  constructor(config?: Partial<InterceptorConfig>) {
    this.config = {
      enableSmartFiltering: true,
      maxConcurrentRequests: 3,
      cachePatterns: true,
      timeout: 5000,
      ...config
    };

    // 保存原始方法引用
    this.originalFetch = window.fetch;
    this.originalXHROpen = XMLHttpRequest.prototype.open;
    this.originalXHRSend = XMLHttpRequest.prototype.send;

    this.initializePlatformRules();
  }

  /**
   * 初始化拦截器
   */
  async initialize(): Promise<void> {
    if (this.config.cachePatterns) {
      this.precompilePatterns();
    }
    console.log('🎯 [SubtitleNetworkInterceptor] 拦截器初始化完成');
  }

  /**
   * 注册拦截规则
   */
  registerInterceptRule(platform: SupportedPlatform, rule: InterceptRule): void {
    const rules = this.interceptRules.get(platform) || [];
    rules.push(rule);
    this.interceptRules.set(platform, rules.sort((a, b) => b.priority - a.priority));
    
    console.log(`📝 [SubtitleNetworkInterceptor] 注册拦截规则: ${platform}`, rule.urlPattern);
  }

  /**
   * 开始网络拦截
   */
  async startInterception(): Promise<void> {
    if (this.isActive) {
      console.warn('⚠️ [SubtitleNetworkInterceptor] 拦截器已在运行中');
      return;
    }

    try {
      // 拦截 Fetch API
      this.interceptFetch();
      
      // 拦截 XMLHttpRequest
      this.interceptXHR();

      // 注册 webRequest 监听器（如果有权限）
      if (typeof browser !== 'undefined' && browser?.webRequest) {
        await this.setupWebRequestInterception();
      }

      this.isActive = true;
      console.log('🎯 [SubtitleNetworkInterceptor] 网络拦截已启动');
    } catch (error) {
      throw new SubtitleError(
        SubtitleErrorType.NETWORK_INTERCEPTION_FAILED,
        '启动网络拦截失败',
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * 停止网络拦截
   */
  stopInterception(): void {
    if (!this.isActive) return;

    try {
      // 恢复原始 API
      this.restoreOriginalAPIs();

      // 移除 webRequest 监听器
      if (typeof browser !== 'undefined' && browser?.webRequest) {
        browser.webRequest.onBeforeRequest.removeListener(this.handleWebRequest);
      }

      this.isActive = false;
      console.log('🛑 [SubtitleNetworkInterceptor] 网络拦截已停止');
    } catch (error) {
      console.error('❌ [SubtitleNetworkInterceptor] 停止拦截时出错:', error);
    }
  }

  /**
   * 获取拦截状态
   */
  getInterceptionStatus(): InterceptionStatus {
    return this.isActive ? 'active' : 'inactive';
  }

  /**
   * 设置捕获回调
   */
  setCaptureCallback(callback: (data: SubtitleData) => void): void {
    this.captureCallback = callback;
  }

  /**
   * 拦截 Fetch API
   */
  private interceptFetch(): void {
    const self = this;
    
    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
      const url = typeof input === 'string' ? input : 
                  input instanceof URL ? input.href : 
                  (input as Request).url;
      
      // 检查是否匹配字幕请求
      const matchResult = self.checkSubtitleRequest(url, 'GET');
      
      // 调用原始 fetch
      const response = await self.originalFetch.call(this, input, init);
      
      // 如果匹配且响应成功，处理字幕数据
      if (matchResult && response.ok) {
        try {
          const responseClone = response.clone();
          const text = await responseClone.text();
          
          const subtitleData: SubtitleData = {
            ...matchResult,
            rawData: text,
            timestamp: Date.now()
          };
          
          self.captureCallback?.(subtitleData);
        } catch (error) {
          console.error('🚨 [SubtitleNetworkInterceptor] 处理 Fetch 响应失败:', error);
        }
      }
      
      return response;
    };
  }

  /**
   * 拦截 XMLHttpRequest
   */
  private interceptXHR(): void {
    const self = this;
    
    XMLHttpRequest.prototype.open = function(
      method: string, 
      url: string | URL, 
      async?: boolean,
      user?: string | null,
      password?: string | null
    ) {
      const urlString = typeof url === 'string' ? url : url.href;
      (this as any)._subtitleUrl = urlString;
      (this as any)._subtitleMethod = method.toUpperCase();
      
      return self.originalXHROpen.call(this, method, url, async, user, password);
    };
    
    XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null) {
      const urlString = (this as any)._subtitleUrl;
      const method = (this as any)._subtitleMethod;
      
      if (urlString) {
        const matchResult = self.checkSubtitleRequest(urlString, method);
        
        if (matchResult) {
          this.addEventListener('load', function() {
            if (this.status >= 200 && this.status < 300) {
              try {
                const subtitleData: SubtitleData = {
                  ...matchResult,
                  rawData: this.responseText,
                  timestamp: Date.now()
                };
                
                self.captureCallback?.(subtitleData);
              } catch (error) {
                console.error('🚨 [SubtitleNetworkInterceptor] 处理 XHR 响应失败:', error);
              }
            }
          });
        }
      }
      
      return self.originalXHRSend.call(this, body);
    };
  }

  /**
   * 设置 webRequest 拦截（需要权限）
   */
  private async setupWebRequestInterception(): Promise<void> {
    if (!browser?.webRequest) return;

    const urls = Array.from(this.interceptRules.values())
      .flat()
      .map(rule => rule.urlPattern.source)
      .filter(pattern => pattern.includes('*'));

    if (urls.length > 0) {
      browser.webRequest.onBeforeRequest.addListener(
        this.handleWebRequest.bind(this),
        { urls },
        ['requestBody']
      );
    }
  }

  /**
   * 处理 webRequest 事件
   */
  private handleWebRequest(details: chrome.webRequest.WebRequestBodyDetails): void {
    try {
      const matchResult = this.checkSubtitleRequest(details.url, details.method);
      if (matchResult) {
        console.log('🎯 [SubtitleNetworkInterceptor] webRequest 匹配:', details.url);
        // webRequest 拦截需要后续处理响应数据
      }
    } catch (error) {
      console.error('🚨 [SubtitleNetworkInterceptor] webRequest 处理失败:', error);
    }
  }

  /**
   * 检查是否为字幕请求
   */
  private checkSubtitleRequest(url: string, method: string): Omit<SubtitleData, 'rawData' | 'timestamp'> | null {
    if (!this.config.enableSmartFiltering || this.shouldCheckUrl(url)) {
      for (const [platform, rules] of this.interceptRules.entries()) {
        for (const rule of rules) {
          if (rule.method === method && this.testUrlPattern(rule.urlPattern, url)) {
            return {
              platform,
              format: this.detectFormatFromUrl(url, platform),
              url,
              videoId: this.extractVideoId(url, platform)
            };
          }
        }
      }
    }
    
    return null;
  }

  /**
   * 测试 URL 模式
   */
  private testUrlPattern(pattern: RegExp, url: string): boolean {
    try {
      return pattern.test(url);
    } catch (error) {
      console.error('🚨 [SubtitleNetworkInterceptor] URL 模式测试失败:', error);
      return false;
    }
  }

  /**
   * 智能过滤：判断是否需要检查此 URL
   */
  private shouldCheckUrl(url: string): boolean {
    // 基本过滤：只检查可能的字幕相关 URL
    const subtitleKeywords = ['subtitle', 'timedtext', 'caption', 'srt', 'vtt', 'ass'];
    const lowerUrl = url.toLowerCase();
    
    return subtitleKeywords.some(keyword => lowerUrl.includes(keyword)) ||
           lowerUrl.includes('translate') ||
           lowerUrl.includes('transcript');
  }

  /**
   * 从 URL 检测字幕格式
   */
  private detectFormatFromUrl(url: string, platform: SupportedPlatform): SubtitleFormat {
    const lowerUrl = url.toLowerCase();
    
    // 平台特定检测
    if (platform === SupportedPlatform.YOUTUBE) {
      if (lowerUrl.includes('timedtext') || lowerUrl.includes('api/transcript')) {
        return SubtitleFormat.YOUTUBE_JSON;
      }
    }
    
    // 通用格式检测
    if (lowerUrl.includes('.vtt') || lowerUrl.includes('webvtt')) {
      return SubtitleFormat.VTT;
    }
    if (lowerUrl.includes('.srt')) {
      return SubtitleFormat.SRT;
    }
    if (lowerUrl.includes('.ass')) {
      return SubtitleFormat.ASS;
    }
    
    return SubtitleFormat.UNKNOWN;
  }

  /**
   * 提取视频 ID
   */
  private extractVideoId(url: string, platform: SupportedPlatform): string | undefined {
    try {
      switch (platform) {
        case SupportedPlatform.YOUTUBE:
          const youtubeMatch = url.match(/[?&]v=([^&]+)/);
          return youtubeMatch?.[1];
          
        case SupportedPlatform.NETFLIX:
          const netflixMatch = url.match(/\/watch\/(\d+)/);
          return netflixMatch?.[1];
          
        default:
          return undefined;
      }
    } catch (error) {
      console.error('🚨 [SubtitleNetworkInterceptor] 提取视频 ID 失败:', error);
      return undefined;
    }
  }

  /**
   * 预编译正则表达式模式
   */
  private precompilePatterns(): void {
    for (const rules of this.interceptRules.values()) {
      for (const rule of rules) {
        const key = rule.urlPattern.source;
        if (!this.compiledPatterns.has(key)) {
          this.compiledPatterns.set(key, rule.urlPattern);
        }
      }
    }
    
    console.log(`📦 [SubtitleNetworkInterceptor] 预编译了 ${this.compiledPatterns.size} 个模式`);
  }

  /**
   * 恢复原始 API
   */
  private restoreOriginalAPIs(): void {
    window.fetch = this.originalFetch;
    XMLHttpRequest.prototype.open = this.originalXHROpen;
    XMLHttpRequest.prototype.send = this.originalXHRSend;
  }

  /**
   * 初始化默认平台规则
   */
  private initializePlatformRules(): void {
    // YouTube 规则
    this.registerInterceptRule(SupportedPlatform.YOUTUBE, {
      urlPattern: /.*\/api\/timedtext.*/,
      method: 'GET',
      priority: 10,
      extractSubtitleData: async (response) => ({
        platform: SupportedPlatform.YOUTUBE,
        format: SubtitleFormat.YOUTUBE_JSON,
        rawData: await response.text(),
        url: response.url,
        timestamp: Date.now()
      })
    });

    // YouTube 替代 API
    this.registerInterceptRule(SupportedPlatform.YOUTUBE, {
      urlPattern: /.*youtube\.com.*transcript.*/,
      method: 'GET', 
      priority: 8,
      extractSubtitleData: async (response) => ({
        platform: SupportedPlatform.YOUTUBE,
        format: SubtitleFormat.VTT,
        rawData: await response.text(),
        url: response.url,
        timestamp: Date.now()
      })
    });

    // Netflix 规则
    this.registerInterceptRule(SupportedPlatform.NETFLIX, {
      urlPattern: /.*\/nq\/cadmium-playercore.*/,
      method: 'GET',
      priority: 10,
      extractSubtitleData: async (response) => ({
        platform: SupportedPlatform.NETFLIX,
        format: SubtitleFormat.VTT,
        rawData: await response.text(),
        url: response.url,
        timestamp: Date.now()
      })
    });

    // 通用 VTT 规则
    this.registerInterceptRule(SupportedPlatform.GENERIC, {
      urlPattern: /.*\.vtt(\?.*)?$/,
      method: 'GET',
      priority: 5,
      extractSubtitleData: async (response) => ({
        platform: SupportedPlatform.GENERIC,
        format: SubtitleFormat.VTT,
        rawData: await response.text(),
        url: response.url,
        timestamp: Date.now()
      })
    });

    // 通用 SRT 规则
    this.registerInterceptRule(SupportedPlatform.GENERIC, {
      urlPattern: /.*\.srt(\?.*)?$/,
      method: 'GET',
      priority: 5,
      extractSubtitleData: async (response) => ({
        platform: SupportedPlatform.GENERIC,
        format: SubtitleFormat.SRT,
        rawData: await response.text(),
        url: response.url,
        timestamp: Date.now()
      })
    });

    console.log(`🔧 [SubtitleNetworkInterceptor] 初始化了 ${this.interceptRules.size} 个平台的拦截规则`);
  }

  /**
   * 销毁拦截器
   */
  destroy(): void {
    this.stopInterception();
    this.interceptRules.clear();
    this.compiledPatterns.clear();
    this.captureCallback = undefined;
    
    console.log('🗑️ [SubtitleNetworkInterceptor] 拦截器已销毁');
  }
}